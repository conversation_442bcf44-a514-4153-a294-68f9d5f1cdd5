import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  RefreshControl,
  Dimensions
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import * as Animatable from 'react-native-animatable';
import { LinearGradient } from 'expo-linear-gradient';
import { useAuth } from '../context/AuthContext';
import { theme } from '../theme/theme';

const { width } = Dimensions.get('window');

export default function HomeScreen({ navigation }: any) {
  const { user } = useAuth();
  const [refreshing, setRefreshing] = useState(false);
  const [greeting, setGreeting] = useState('');

  useEffect(() => {
    setGreetingMessage();
  }, []);

  const setGreetingMessage = () => {
    const hour = new Date().getHours();
    if (hour < 12) {
      setGreeting('Good morning');
    } else if (hour < 17) {
      setGreeting('Good afternoon');
    } else {
      setGreeting('Good evening');
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    // Simulate refresh
    setTimeout(() => {
      setRefreshing(false);
    }, 1000);
  };
  const quickActions = [
    {
      id: 1,
      title: 'Browse Institutions',
      subtitle: '150+ Universities & Colleges',
      icon: 'school',
      gradient: [theme.colors.primary, theme.colors.primaryContainer],
      onPress: () => navigation.navigate('Institutions')
    },
    {
      id: 2,
      title: 'Find Bursaries',
      subtitle: '75+ Funding Opportunities',
      icon: 'wallet',
      gradient: [theme.colors.secondary, theme.colors.secondaryContainer],
      onPress: () => navigation.navigate('Bursaries')
    },
    {
      id: 3,
      title: 'My Applications',
      subtitle: 'Track Your Progress',
      icon: 'document-text',
      gradient: [theme.colors.tertiary, '#64B5F6'],
      onPress: () => navigation.navigate('Applications')
    },
    {
      id: 4,
      title: 'Profile Settings',
      subtitle: 'Manage Your Info',
      icon: 'person',
      gradient: ['#9C27B0', '#BA68C8'],
      onPress: () => navigation.navigate('Profile')
    }
  ];

  const stats = [
    { label: 'Institutions', value: '150+', icon: 'school' },
    { label: 'Bursaries', value: '75+', icon: 'cash' },
    { label: 'Students Helped', value: '2,847', icon: 'people' }
  ];

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView
        style={styles.scrollView}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        showsVerticalScrollIndicator={false}
      >
        {/* Header Section */}
        <LinearGradient
          colors={[theme.colors.primary, theme.colors.primaryContainer]}
          style={styles.headerGradient}
        >
          <Animatable.View animation="fadeInDown" style={styles.headerContent}>
            <Text style={styles.greetingText}>{greeting}!</Text>
            <Text style={styles.userNameText}>
              {user?.email?.split('@')[0] || 'Student'}
            </Text>
            <Text style={styles.headerSubtitle}>
              Ready to explore your future? 🚀
            </Text>
          </Animatable.View>
        </LinearGradient>

        {/* Stats Section */}
        <View style={styles.statsContainer}>
          <Text style={styles.sectionTitle}>📊 Platform Statistics</Text>
          <View style={styles.statsGrid}>
            {stats.map((stat, index) => (
              <View key={index} style={styles.statCard}>
                <Ionicons name={stat.icon} size={24} color="#007A4D" />
                <Text style={styles.statValue}>{stat.value}</Text>
                <Text style={styles.statLabel}>{stat.label}</Text>
              </View>
            ))}
          </View>
        </View>

        {/* Quick Actions */}
        <Animatable.View animation="fadeInUp" delay={300} style={styles.actionsContainer}>
          <Text style={styles.sectionTitle}>Quick Actions</Text>
          <View style={styles.actionsGrid}>
            {quickActions.map((action, index) => (
              <Animatable.View
                key={action.id}
                animation="fadeInUp"
                delay={400 + (index * 100)}
              >
                <TouchableOpacity
                  style={styles.actionCard}
                  onPress={action.onPress}
                  activeOpacity={0.8}
                >
                  <LinearGradient
                    colors={action.gradient}
                    style={styles.actionGradient}
                    start={{ x: 0, y: 0 }}
                    end={{ x: 1, y: 1 }}
                  >
                    <View style={styles.actionContent}>
                      <Ionicons name={action.icon} size={32} color="white" />
                      <View style={styles.actionTextContainer}>
                        <Text style={styles.actionTitle}>{action.title}</Text>
                        <Text style={styles.actionSubtitle}>{action.subtitle}</Text>
                      </View>
                      <Ionicons name="chevron-forward" size={24} color="rgba(255,255,255,0.8)" />
                    </View>
                  </LinearGradient>
                </TouchableOpacity>
              </Animatable.View>
            ))}
          </View>
        </Animatable.View>

        {/* Recent Updates */}
        <View style={styles.updatesContainer}>
          <Text style={styles.sectionTitle}>📢 Recent Updates</Text>
          <View style={styles.updateCard}>
            <View style={styles.updateHeader}>
              <Ionicons name="megaphone" size={20} color="#007A4D" />
              <Text style={styles.updateTitle}>🤖 Automation Active!</Text>
            </View>
            <Text style={styles.updateText}>
              Our system discovered 5 new institutions and 12 new bursaries today!
            </Text>
            <Text style={styles.updateTime}>Just now</Text>
          </View>
          
          <View style={styles.updateCard}>
            <View style={styles.updateHeader}>
              <Ionicons name="card" size={20} color="#FF6B35" />
              <Text style={styles.updateTitle}>💳 Payment System Live</Text>
            </View>
            <Text style={styles.updateText}>
              EFT, Capitec Pay, and mobile payments now available for applications.
            </Text>
            <Text style={styles.updateTime}>2 hours ago</Text>
          </View>
        </View>

        {/* Help Section */}
        <View style={styles.helpContainer}>
          <Text style={styles.sectionTitle}>❓ Need Help?</Text>
          <TouchableOpacity style={styles.helpCard}>
            <Ionicons name="help-circle" size={24} color="#007A4D" />
            <View style={styles.helpText}>
              <Text style={styles.helpTitle}>How to Apply</Text>
              <Text style={styles.helpSubtitle}>Step-by-step guide</Text>
            </View>
            <Ionicons name="chevron-forward" size={20} color="#666" />
          </TouchableOpacity>
          
          <TouchableOpacity style={styles.helpCard}>
            <Ionicons name="call" size={24} color="#007A4D" />
            <View style={styles.helpText}>
              <Text style={styles.helpTitle}>Contact Support</Text>
              <Text style={styles.helpSubtitle}>+27 (0) 11 123 4567</Text>
            </View>
            <Ionicons name="chevron-forward" size={20} color="#666" />
          </TouchableOpacity>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#1a1a1a',
  },
  scrollView: {
    flex: 1,
  },
  welcomeSection: {
    padding: 20,
    alignItems: 'center',
    backgroundColor: '#2a2a2a',
    marginBottom: 20,
  },
  welcomeTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: 'white',
    textAlign: 'center',
    marginBottom: 8,
  },
  welcomeSubtitle: {
    fontSize: 16,
    color: 'rgba(255,255,255,0.7)',
    textAlign: 'center',
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: 'white',
    marginBottom: 15,
    paddingHorizontal: 20,
  },
  statsContainer: {
    marginBottom: 30,
  },
  statsGrid: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingHorizontal: 20,
  },
  statCard: {
    alignItems: 'center',
    backgroundColor: '#2a2a2a',
    padding: 15,
    borderRadius: 12,
    minWidth: 80,
  },
  statValue: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#007A4D',
    marginTop: 8,
  },
  statLabel: {
    fontSize: 12,
    color: 'rgba(255,255,255,0.6)',
    marginTop: 4,
    textAlign: 'center',
  },
  actionsContainer: {
    marginBottom: 30,
  },
  actionsGrid: {
    paddingHorizontal: 20,
  },
  actionCard: {
    backgroundColor: '#2a2a2a',
    padding: 20,
    borderRadius: 12,
    marginBottom: 12,
    borderLeftWidth: 4,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  actionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  actionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: 'white',
    marginLeft: 12,
  },
  actionSubtitle: {
    fontSize: 12,
    color: 'rgba(255,255,255,0.6)',
    marginLeft: 40,
    flex: 1,
  },
  updatesContainer: {
    marginBottom: 30,
  },
  updateCard: {
    backgroundColor: '#2a2a2a',
    padding: 15,
    borderRadius: 12,
    marginHorizontal: 20,
    marginBottom: 10,
  },
  updateHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  updateTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    color: 'white',
    marginLeft: 8,
  },
  updateText: {
    fontSize: 13,
    color: 'rgba(255,255,255,0.7)',
    lineHeight: 18,
    marginBottom: 8,
  },
  updateTime: {
    fontSize: 11,
    color: 'rgba(255,255,255,0.5)',
  },
  helpContainer: {
    marginBottom: 30,
  },
  helpCard: {
    backgroundColor: '#2a2a2a',
    padding: 15,
    borderRadius: 12,
    marginHorizontal: 20,
    marginBottom: 10,
    flexDirection: 'row',
    alignItems: 'center',
  },
  helpText: {
    flex: 1,
    marginLeft: 12,
  },
  helpTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    color: 'white',
  },
  helpSubtitle: {
    fontSize: 12,
    color: 'rgba(255,255,255,0.6)',
    marginTop: 2,
  },
});
