/** @type {import('next').NextConfig} */
const nextConfig = {
  // Disable linting during build to avoid hanging
  eslint: {
    ignoreDuringBuilds: true,
  },
  // Disable type checking during build to avoid hanging
  typescript: {
    ignoreBuildErrors: true,
  },
  images: {
    domains: [
      'images.unsplash.com',
      'kioqgrvnolerzffqdwmt.supabase.co',
      'localhost'
    ],
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'localhost',
      },
      {
        protocol: 'https',
        hostname: '**.supabase.co',
      },
      {
        protocol: 'https',
        hostname: 'supabase.co',
      },
      {
        protocol: 'https',
        hostname: 'images.unsplash.com',
      }
    ],
  },
  // Production optimizations
  swcMinify: true,
  compress: true,
  poweredByHeader: false,

  // Webpack configuration to handle Supabase bundling
  webpack: (config, { isServer }) => {
    if (!isServer) {
      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false,
        net: false,
        tls: false,
      }
    }
    return config
  },

  // Experimental features for better bundling
  experimental: {
    serverComponentsExternalPackages: ['@supabase/supabase-js'],
  },

  // PWA Configuration for mobile app
  async headers() {
    return [
      {
        source: '/manifest.json',
        headers: [
          {
            key: 'Content-Type',
            value: 'application/manifest+json',
          },
        ],
      },
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff'
          },
          {
            key: 'X-Frame-Options',
            value: 'DENY'
          },
          {
            key: 'X-XSS-Protection',
            value: '1; mode=block'
          }
        ]
      }
    ]
  },
}

module.exports = nextConfig
