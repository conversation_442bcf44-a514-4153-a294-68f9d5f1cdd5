# 📸 Apply4Me Image Download Guide

## 🎯 **Priority Images to Download**

### **1. Hero Section Background (URGENT)**
**File**: `/public/images/hero/hero-background.jpg`
**Search Terms**: "south african students graduation", "diverse university students", "african students success"
**Specifications**: 1920x1080px, high quality
**Recommended**: Unsplash photo of diverse South African students celebrating graduation

### **2. Testimonial Profile Photos (HIGH PRIORITY)**
**Directory**: `/public/images/testimonials/`
**Files Needed**:
- `thabo-mthembu.jpg` - Young African male student
- `nomsa-dlamini.jpg` - Young African female student  
- `sipho-ndlovu.jpg` - Young African male student
- `lerato-mokwena.jpg` - Young African female student
- `micha<PERSON>-van-der-merwe.jpg` - Young mixed-race male student
- `zanele-khumalo.jpg` - Young African female student

**Specifications**: 400x400px, square format, professional headshots
**Search Terms**: "african student portrait", "south african university student", "young professional headshot"

### **3. Feature Section Images (MEDIUM PRIORITY)**
**Directory**: `/public/images/features/`
**Files Needed**:
- `application-process.jpg` - Students filling out forms/applications
- `career-guidance.jpg` - Career counseling or guidance session
- `document-management.jpg` - Organized documents or digital files
- `payment-security.jpg` - Secure payment or financial concept
- `mobile-app.jpg` - Students using mobile phones for applications
- `support-team.jpg` - Customer support or help desk

**Specifications**: 600x400px, modern and professional
**Search Terms**: "student application process", "career guidance", "mobile education app"

### **4. Institution Showcase (MEDIUM PRIORITY)**
**Directory**: `/public/images/institutions/`
**Files Needed**:
- `university-campus.jpg` - Beautiful South African university campus
- `tvet-college.jpg` - TVET college building or students
- `students-studying.jpg` - Students in library or study group
- `graduation-ceremony.jpg` - Graduation ceremony scene
- `campus-life.jpg` - Students on campus, social activities

**Specifications**: 800x600px, showcasing South African educational institutions
**Search Terms**: "south african university campus", "african students studying", "university graduation south africa"

### **5. Background Images (LOW PRIORITY)**
**Directory**: `/public/images/backgrounds/`
**Files Needed**:
- `section-bg-1.jpg` - Subtle educational background
- `section-bg-2.jpg` - Abstract or geometric pattern
- `testimonials-bg.jpg` - Light, professional background

**Specifications**: 1920x1080px, subtle and not distracting

## 🔍 **Specific Search Recommendations**

### **Unsplash Search Terms:**
1. "south african students"
2. "african university graduation"
3. "diverse students studying"
4. "university campus south africa"
5. "young african professionals"
6. "education technology africa"
7. "student success celebration"
8. "african youth education"

### **Pexels Search Terms:**
1. "african students"
2. "university graduation"
3. "students studying together"
4. "education success"
5. "young professionals africa"

### **Pixabay Search Terms:**
1. "african education"
2. "student graduation"
3. "university campus"
4. "education success"

## 📋 **Download Checklist**

### **Step 1: Download Hero Image**
- [ ] Search "south african students graduation" on Unsplash
- [ ] Download high-resolution image (1920x1080px minimum)
- [ ] Save as `/public/images/hero/hero-background.jpg`
- [ ] Optimize file size (aim for under 500KB)

### **Step 2: Download Testimonial Photos**
- [ ] Search for diverse African student portraits
- [ ] Download 6 different photos (3 male, 3 female)
- [ ] Crop to 400x400px square format
- [ ] Save with appropriate names in `/public/images/testimonials/`

### **Step 3: Download Feature Images**
- [ ] Search for education-related process images
- [ ] Download 6 feature images as listed above
- [ ] Resize to 600x400px
- [ ] Save in `/public/images/features/`

### **Step 4: Download Institution Images**
- [ ] Search for South African university campuses
- [ ] Download 5 institution-related images
- [ ] Resize to 800x600px
- [ ] Save in `/public/images/institutions/`

## 🛠 **After Downloading**

1. **Optimize Images**: Use tools like TinyPNG or ImageOptim to compress
2. **Test Loading**: Check that images load properly on the website
3. **Update Alt Text**: Ensure all images have descriptive alt text
4. **Check Mobile**: Verify images look good on mobile devices

## 📝 **Attribution Requirements**

If using images that require attribution:
1. Create `/public/images/CREDITS.md`
2. List photographer names and Unsplash/Pexels URLs
3. Add attribution in website footer if required

## 🚀 **Quick Start Commands**

After downloading images, run:
```bash
# Optimize images (if you have imagemin installed)
npm run optimize-images

# Build and test
npm run build
npm run dev
```

## 💡 **Pro Tips**

1. **Consistency**: Choose images with similar lighting and color tones
2. **Diversity**: Ensure images represent South Africa's diverse student population
3. **Quality**: Always download the highest resolution available
4. **Relevance**: Images should clearly relate to education and student success
5. **Mobile-First**: Test how images look on mobile devices

## 🎨 **Color Palette to Match**

When selecting images, look for photos that complement Apply4Me's colors:
- **Primary Green**: #006A4E (SA Green)
- **Gold Accent**: #FFB612 (SA Gold)  
- **Blue Accent**: #002395 (SA Blue)
- **Neutral**: Clean whites and grays

Choose images with these color tones or neutral backgrounds that won't clash.
