name: Deploy Apply4Me to Production

on:
  push:
    branches: [main]
  pull_request:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    name: Test Admin System
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
          
      - name: Install dependencies
        run: npm ci
        
      - name: Run type checking
        run: npm run type-check
        
      - name: Run linting
        run: npm run lint
        
      - name: Build application
        run: npm run build
        env:
          NODE_ENV: production
          REQUIRE_AUTH: true
          NEXT_PUBLIC_SUPABASE_URL: ${{ secrets.NEXT_PUBLIC_SUPABASE_URL }}
          NEXT_PUBLIC_SUPABASE_ANON_KEY: ${{ secrets.NEXT_PUBLIC_SUPABASE_ANON_KEY }}
          SUPABASE_SERVICE_ROLE_KEY: ${{ secrets.SUPABASE_SERVICE_ROLE_KEY }}

  deploy-staging:
    runs-on: ubuntu-latest
    name: Deploy to Staging
    needs: test
    if: github.ref == 'refs/heads/develop'
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Deploy to Vercel (Staging)
        uses: amondnet/vercel-action@v25
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
          vercel-org-id: ${{ secrets.VERCEL_ORG_ID }}
          vercel-project-id: ${{ secrets.VERCEL_PROJECT_ID }}
          scope: ${{ secrets.VERCEL_ORG_ID }}
          
      - name: Health Check (Staging)
        run: |
          sleep 30
          curl -f ${{ secrets.STAGING_URL }}/api/test/admin-system || exit 1

  deploy-production:
    runs-on: ubuntu-latest
    name: Deploy to Production
    needs: test
    if: github.ref == 'refs/heads/main'
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Deploy to Vercel (Production)
        uses: amondnet/vercel-action@v25
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
          vercel-org-id: ${{ secrets.VERCEL_ORG_ID }}
          vercel-project-id: ${{ secrets.VERCEL_PROJECT_ID }}
          vercel-args: '--prod'
          scope: ${{ secrets.VERCEL_ORG_ID }}
          
      - name: Health Check (Production)
        run: |
          sleep 30
          curl -f ${{ secrets.PRODUCTION_URL }}/api/test/admin-system || exit 1
          
      - name: Notify Success
        if: success()
        run: |
          echo "🎉 Apply4Me deployed successfully to production!"
          echo "🔗 Admin Interface: ${{ secrets.PRODUCTION_URL }}/admin/test-users"
          echo "🔗 Health Check: ${{ secrets.PRODUCTION_URL }}/api/test/admin-system"
