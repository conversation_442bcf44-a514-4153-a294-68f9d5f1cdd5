#!/bin/bash

echo "🚀 Apply4Me Deployment Script"
echo "=============================="

# Step 1: Add all files
echo "📁 Adding files to git..."
git add .

# Step 2: Commit changes
echo "💾 Committing changes..."
git commit -m "🚀 Apply4Me v1.0 - Production Ready

✅ Core Features Complete:
- User authentication & registration  
- Institution browsing & search
- Application submission flow
- Payment processing
- User dashboard
- Responsive design

✅ Production Optimizations:
- Removed debug buttons & console logs
- Fixed TypeScript errors
- Optimized build configuration
- Security headers configured

🇿🇦 Ready to help South African students!"

# Step 3: Set up remote (you'll need to replace with your GitHub URL)
echo "🔗 Setting up GitHub remote..."
echo "⚠️  REPLACE 'yourusername' with your actual GitHub username!"
echo "git remote add origin https://github.com/BhekumusaEric/apply4me.git"

# Step 4: Push to GitHub
echo "📤 Ready to push to GitHub..."
echo "Run these commands after creating your GitHub repository:"
echo ""
echo "git remote add origin https://github.com/BhekumusaEric/apply4me.git"
echo "git branch -M main"
echo "git push -u origin main"
echo ""
echo "🎉 Then deploy to Vercel at: https://vercel.com"
