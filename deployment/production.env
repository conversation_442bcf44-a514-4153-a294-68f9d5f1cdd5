# Apply4Me Production Environment Configuration
# Copy these variables to your deployment platform (Vercel, Netlify, etc.)

# Environment
NODE_ENV=production
NEXT_PUBLIC_APP_URL=https://your-production-domain.com

# Authentication
REQUIRE_AUTH=true

# Supabase Configuration (same as development)
NEXT_PUBLIC_SUPABASE_URL=https://kioqgrvnolerzffqdwmt.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=yeyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imtpb3FncnZub2xlcnpmZnFkd210Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgxODM4MDcsImV4cCI6MjA2Mzc1OTgwN30.CD2PAbcklmqMf8NlCK_zdttAy5sMfesAaeBmyZCVwGk
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imtpb3FncnZub2xlcnpmZnFkd210Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0ODE4MzgwNywiZXhwIjoyMDYzNzU5ODA3fQ.TwVDEZ1uQo8_yOYsHvZNOklGZFhY3-vvV7pr56nqPOs
SUPABASE_JWT_SECRET=Onb5to1SughJv8dYDcmyU4gpzpdY+Mi7xKxgqkj7aU2Tc2XtW4fJn6JAih5Qa+M7Q3sxOd/9ZelYarV5h7MROw==

# App Configuration
NEXT_PUBLIC_APP_NAME=Apply4Me

# Payment Configuration (PayFast)
PAYFAST_MERCHANT_ID=10000100
PAYFAST_MERCHANT_KEY=46f0cd694581a
PAYFAST_PASSPHRASE=jt7NOE43FZPn

# Payment Configuration (Yoco - Optional)
NEXT_PUBLIC_YOCO_PUBLIC_KEY=your_yoco_public_key
YOCO_SECRET_KEY=your_yoco_secret_key

# WhatsApp Integration (Optional)
WHATSAPP_API_TOKEN=your_whatsapp_api_token
WHATSAPP_PHONE_NUMBER_ID=your_whatsapp_phone_number_id

# Email Configuration (Optional)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your_app_password

# File Upload Configuration
NEXT_PUBLIC_MAX_FILE_SIZE=10485760
NEXT_PUBLIC_ALLOWED_FILE_TYPES=pdf,jpg,jpeg,png,doc,docx

# Admin User Management
ADMIN_EMAILS=<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>
