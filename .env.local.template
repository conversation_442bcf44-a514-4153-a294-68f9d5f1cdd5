# Google Services Configuration
# Replace these values with your actual credentials

# Google Cloud Project ID (from Google Cloud Console)
GOOGLE_CLOUD_PROJECT_ID=apply4me

# Path to your service account JSON file (absolute path)
# Example: /Users/<USER>/apply4me/credentials/service-account-key.json
GOOGLE_APPLICATION_CREDENTIALS=/path/to/your/service-account-key.json

# OAuth 2.0 Credentials (from Google Cloud Console)
GOOGLE_CLIENT_ID=your_google_oauth_client_id.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=your_google_oauth_client_secret

# NextAuth Configuration
# Generate with: openssl rand -base64 32
NEXTAUTH_SECRET=your_random_secret_key_here
NEXTAUTH_URL=http://localhost:3000

# Google Services Features (optional)
ENABLE_GOOGLE_DRIVE=true
ENABLE_GOOGLE_SHEETS=true
ENABLE_GOOGLE_CALENDAR=true

# Existing Supabase Configuration (keep your existing values)
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

# Other existing environment variables...
# (copy from your existing .env.local file)
