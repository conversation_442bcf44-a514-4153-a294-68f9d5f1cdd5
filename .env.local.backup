# Apply4Me Environment Variables
NEXT_PUBLIC_SUPABASE_URL=https://kioqgrvnolerzffqdwmt.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.CD2PAbcklmqMf8NlCK_zdttAy5sMfesAaeBmyZCVwGk
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************.TwVDEZ1uQo8_yOYsHvZNOklGZFhY3-vvV7pr56nqPOs
NEXT_PUBLIC_APP_NAME=Apply4Me

# Development settings
REQUIRE_AUTH=false
NODE_ENV=development

# Google Services Configuration
GOOGLE_CLOUD_PROJECT_ID=apply4me-462216
GOOGLE_APPLICATION_CREDENTIALS=/Users/<USER>/apply4me/credentials/service-account-key.json
GOOGLE_CLIENT_ID=***********-7vcnhu7ai7nn357qkipfm0tpqe6ig2dk.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=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
GOOGLE_API_KEY=AIzaSyDvMRyvKTguXlGlaq3No_YNgm1oZcvgp0M
GOOGLE_UNRESTRICTED_API_ACCESS=AIzaSyDvMRyvKTguXlGlaq3No_YNgm1oZcvgp0M

# NextAuth Configuration
NEXTAUTH_SECRET=l8QxN1rSk/fvXCNOQl3Lud+7sVwdKl/lOGxDE7pNCOg=
NEXTAUTH_URL=http://localhost:3000

# Google Services Features
ENABLE_GOOGLE_DRIVE=true
ENABLE_GOOGLE_SHEETS=true
ENABLE_GOOGLE_CALENDAR=true
